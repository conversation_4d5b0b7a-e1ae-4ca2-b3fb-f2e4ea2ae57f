/*USing Media Queries*/
/*.card {
    display: grid;
    grid-template-columns: 3fr 1fr;
    border-bottom: 1px solid #ccc;
    padding: 3rem 1rem;
    column-gap: 1rem;
    row-gap: 0.5rem;
}
.card img {
    grid-column: 2/3;
    grid-row: 1/3;
}
.card h2 {
    margin: 0;
    grid-column: 1/2;
}
.card:first-child {
    display: block;
}

@media (min-width: 500px) {
    .photo-cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto 1fr auto;
    }
    .card {
        grid-row: span 3;
        grid-template-columns: 2fr 1fr;
        grid-template-rows: subgrid;
    }
    .card:first-child {
        grid-column: 1/3;
    }
    .card .text {
        grid-column: 1/3;
        grid-row: 2/3;
    }
    .card .link {
        grid-column: 1/3;
    }
    .card:nth-child(2),
    .card:nth-child(4) {
        border-right: 1px solid #ccc;
    }
}
@media (min-width: 750px) {
    .photo-cards {
        grid-template-columns: repeat(4, 1fr);
        grid-template-rows: repeat(3, auto);
        border-top: 1px solid #ccc;
    }
    .card {
        grid-template-columns: 1fr;
        grid-template-rows: minmax(0, 0.7fr) minmax(0, 1fr) auto;
    }
    .card:first-child {
        grid-column: 1/4;
        grid-row: 1/3;
        border-right: 1px solid #ccc;
    }
    .card:nth-child(2) {
        grid-column: 4/5;
        grid-row: 1/2;
    }
    .card:nth-child(3) {
        grid-column: 4/5;
        grid-row: 2/3;
    }
    .card:nth-child(2) .text,
    .card:nth-child(3) .text {
        display: none;
    }
    .card:nth-child(2) img,
    .card:nth-child(3) img {
        grid-column: 1/2;
    }
    .card:nth-child(4) {
        grid-column: 1/3;
        grid-row: 3/4;
        grid-template-columns: subgrid;
        border-right: 1px solid #ccc;
    }
    .card:nth-child(5) {
        grid-column: 3/5;
        grid-row: 3/4;
        grid-template-columns: subgrid;
    }

}
@media (min-width: 875px) {
    .card {
        grid-template-columns: 1fr;
        grid-template-rows: minmax(0, 1fr) minmax(0, 1fr) auto;
    }
}
@media (min-width: 1000px) {
    .card:nth-child(2),
    .card:nth-child(3),
    .card:nth-child(4),
    .card:nth-child(5) {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: subgrid;
        grid-column: auto;
        grid-row: span 2;
    }
    .card h2 {
        grid-column: 2/3;
        grid-row: 1/2;
    }
    .card img {
        grid-column: 1/2;
        grid-row: 1/2;
    }
    .card .text {
        display: none;
    }
    .card .link {
        grid-column: span 2;
        grid-row: 2/3;
    }
    .card:first-child {
        grid-column: span 2;
        grid-row: span 4;
    }
    .card:first-child .text {
        display: block;
    }

}*/

/* extremely basic formatting */
@layer reset {
    html {
        box-sizing: border-box;
    }

    *,
    *:before,
    *:after {
        box-sizing: inherit;
    }

    body {
        font-family: "Libre Franklin", sans-serif;
        font-size: 1rem;
        line-height: 1.5;
        margin: 0;
        padding: 0;
    }

    img {
        max-width: 100%;
        border-radius: 5px;
    }

    .button {
        background-color: #2d697c;
        padding: 0.5rem;
        border-radius: 5px;
        color: white;
        text-decoration: none;
        font-size: 0.8rem;
    }

    .button:hover {
        background-color: #327c94;
    }

    a {
        color: #2d697c;
    }

    a:hover {
        text-decoration: none;
    }
}

.card{
    container-name: card;
    container-type: inline-size;
}
@container card (min-width: 200px) {
    article {
        padding: 1rem;
        display: grid;
        gap: 1rem;

        > * {
            margin: 0;
        }
        .text {
            display: none;
        }
    }
}
@container card (min-width: 270px) {
    article {
        grid-template-columns: 2fr 1fr;

        img{
            grid-column: 2/3;
            grid-row: 1/2;
        }
        .link{
            grid-column: 1/3;
        }
    }
}
@container card (min-width: 500px) {
    .text{
        display: block;
        grid-column: 1/2;
    }
    article img {
        grid-column: 1/4;
    }

}